\documentclass[final,3p,times,review]{elsarticle}
\usepackage{amssymb}

\usepackage{hyperref}
\usepackage{graphicx}
\usepackage{epstopdf}
\usepackage{caption2}
\usepackage{float}
\usepackage{amsmath}
\usepackage{autobreak}
\usepackage{times,epsfig}
\usepackage{subfigure}
\usepackage{comment}
\usepackage{amssymb}
\usepackage{listings}
\usepackage{booktabs}
\usepackage{url}
\usepackage{balance}
\usepackage{harpoon}%
\usepackage{booktabs}
\usepackage{amsmath}
\usepackage{color}
\usepackage{multirow}
\usepackage{afterpage}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{algorithmic}
\usepackage{verbatim}
\usepackage[table,xcdraw]{xcolor}
\usepackage{lipsum}
\usepackage{lineno}
\usepackage{soul}
\usepackage{amssymb}
\usepackage[title]{appendix}
\newcommand{\new}[1]{\textcolor{black}{#1}}
\newcommand\mycommfont[1]{\footnotesize\ttfamily\textcolor{blue}{#1}}
\SetCommentSty{mycommfont}
\journal{Advances in Applied Energy}

\begin{document}
\begin{frontmatter}
\title{A Personalized AI Agent for Residential Energy Strategy Optimization under Time-of-Use Tariffs
}
\author[EIT]{Zhiqiang Chen\corref{cor1}}
\ead{<EMAIL>}
\address[EIT]{School of Control and Computer Engineering, Eastern Institute of Technology (EIT), 315200 Ningbo, China}
\author[EIT]{Guodong Guo}

\author[dtu]{Xiufeng Liu}
\address[dtu]{Department of Technology, Management and Economics, Technical University of Denmark, 2800 Kgs. Lyngby, Denmark}

\cortext[cor1]{Corresponding author}
\begin{abstract}
Residential energy management under dynamic time-of-use pricing requires optimizing electricity costs while respecting user preferences, but traditional approaches struggle with semantic interpretation of user requirements and appliance behavior heterogeneity. This work presents an intelligent scheduling agent that integrates large language models (LLMs) with cost optimization to bridge the gap between natural language user constraints and formal scheduling algorithms. The system comprises four modules: perception for data preprocessing, cognition for appliance behavior modeling, reasoning for constraint parsing, and optimization for cost-aware scheduling. Evaluation on 20 households from the REFIT dataset demonstrates 24.1\% cost savings under Economy-7 and 26.8\% under Economy-10 tariffs, with 96.8\% accuracy in natural language constraint parsing and 89.3\% success rate in event scheduling. The system achieves full autonomy from unstructured language input to executable scheduling plans, providing a foundation for intelligent energy management in smart homes.
\end{abstract}
\begin{keyword}
Residential Energy Agent, Time-of-Use Tariff Optimization, Appliance Behavior Semantic Modeling, LLM and Reinforcement Learning Integration
\end{keyword}
\end{frontmatter}
\section{Introduction}
The global transition toward carbon neutrality and sustainable energy has intensified the evolution of power systems toward greater intelligence, automation, and user-centric optimization. Among these developments, residential electricity consumption plays a pivotal role as a major end-use load and a core element in smart grid planning. Enabled by smart meters and ubiquitous sensing, household electricity data are now collected at increasingly fine temporal resolutions, offering new possibilities for personalized energy management under dynamic time-of-use (ToU) pricing schemes\cite{chen2023federated,anvari2022data,sykiotis2023performance}. The increasing adoption of ToU tariffs, such as the UK's Economy-7 and Economy-10 schemes, presents significant opportunities for cost reduction through intelligent appliance scheduling, yet realizing this potential requires bridging the gap between complex tariff structures and personalized user preferences.

Despite advances in non-intrusive load monitoring (NILM) that enable appliance-level power disaggregation\cite{he2023msdc,ouzine2022overview}, significant challenges remain in translating low-level power signals into actionable behavioral insights for residential energy optimization. Most household users struggle to interpret appliance-level data and align their consumption patterns with tariff policies, creating a cognitive gap that limits the practical deployment of demand response programs\cite{rafiq2024review,kaselimi2022towards}. The complexity is further magnified by implicit user constraints such as lifestyle routines, noise tolerance, and task deadlines, making appliance scheduling a multi-objective optimization problem that requires semantic understanding of both user preferences and appliance behaviors\cite{ye2023review,muttaqee2024time}.

Current residential energy scheduling approaches face significant limitations in addressing these challenges. Traditional optimization methods rely on pre-structured models that assume complete appliance flexibility and explicitly defined constraints, failing to accommodate the semantic complexity of natural language user preferences\cite{yuan2022multi,mirzaei2025flexibility}. While mathematical formulations such as mixed-integer linear programming (MILP) can achieve optimal solutions\cite{tostado2021milp}, they lack the flexibility to interpret personalized constraints expressed in natural language and typically do not incorporate behavior inference from historical usage patterns. Furthermore, existing agent-based frameworks\cite{wang2025analysing,dasgupta2025smart} often fall short in delivering cost-minimizing strategies that are grounded in semantic user intent and appliance behavior understanding, limiting their real-world applicability.

To address these limitations, this work presents an intelligent residential energy agent that integrates large language models (LLMs) with rule-constrained optimization to enable semantic interpretation of appliance behavior and natural language constraint parsing\cite{russell2016artificial,brown2020language}. The system employs a four-layer architecture encompassing data perception, cognitive modeling, reasoning, and optimization to automatically transform raw power consumption data from the REFIT dataset\cite{murray2016refit} into cost-efficient, constraint-compliant appliance schedules. The major contributions include: (1) a comprehensive framework spanning the full pipeline from raw data processing to executable scheduling recommendations, (2) an LLM-based behavior modeling mechanism for automatic appliance usage pattern extraction, (3) a rule-constrained cost-aware scheduling strategy that balances economic optimization with user preferences, and (4) comprehensive experimental validation demonstrating substantial cost savings while maintaining high constraint compliance across diverse household consumption patterns.

\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/00overview.pdf}
\end{center}
\vspace{-25pt}
\caption{System architecture of the Residential Energy Cognition Agent} 
\label{fig:00overview}
\end{figure*}
The remainder of the paper is organized as follows. Section~\ref{sec:related} reviews relevant literature on non-intrusive load monitoring (NILM), household energy scheduling, and the application of large language models (LLMs) in energy systems. Section~\ref{sec:methodology} describes the overall architecture and methodological components of the proposed system. Section~\ref{sec:experiments} reports experimental evaluations based on real-world datasets. Section~\ref{sec:discussion} discusses system capabilities, limitations, and future research directions. Section~\ref{sec:conclusion} concludes the paper.
\section{Related Work}
\label{sec:related}

\subsection{\textbf{Non-Intrusive Load Monitoring and Household Energy Modeling}}

Non-Intrusive Load Monitoring (NILM) aims to infer the operational states and instantaneous power consumption of individual household appliances from the aggregated whole-home electricity load~\cite{lin2024multiscale,chen2021smart,10121340}. With the growing deployment of smart meters, many studies have utilized high-frequency current and voltage signals to improve NILM modeling and optimization. Representative methods include feature-based clustering, Hidden Markov Models (HMMs)~\cite{wu2021non,kumar2023time}, and deep neural networks~\cite{jia2021sequence}. Although NILM can reconstruct appliance-level usage curves to a certain extent, the majority of research remains focused on the upstream disaggregation task—i.e., identifying appliance states from the aggregate signal—while downstream topics such as user behavior understanding, decision optimization, and personalized guidance have received comparatively little attention\cite{schirmer2022non,fabri2025fostering}.

\subsection{\textbf{ToU-Aware Residential Load Scheduling}}

Optimizing the operation schedules of household appliances has emerged as a key area of research in residential energy management. The objective is to intelligently shift energy consumption away from peak-tariff periods, thereby reducing energy costs~\cite{yuan2022multi,mirzaei2025flexibility}. Proposed approaches include heuristic algorithms such as genetic algorithms and particle swarm optimization\cite{albogamy2022optimal}, as well as mathematical formulations such as mixed-integer linear programming (MILP)\cite{tostado2021milp}.

Meanwhile, Time-of-Use (ToU) pricing policies have been widely adopted in many countries. In the UK, typical residential ToU schemes such as Economy 7 and Economy 10 provide lower electricity prices during specific time windows~\cite{6162795}. Existing work has explored user price sensitivity, optimal tariff design, and the prediction of behavioral responses using historical data~\cite{wu2022impact,fiorotti2024day}. However, conventional scheduling approaches often rely on simplified appliance models that assume complete flexibility in shifting loads. They usually overlook real-world user constraints such as lifestyle routines, noise tolerance, and task deadlines. Furthermore, most studies do not attempt to automatically learn appliance usage habits or abstract user behavioral patterns, thus limiting generalizability and real-world deployment potential\cite{kumar2023multi,zhang2022review}.

\subsection{\textbf{Constraint-Driven Appliance Scheduling}}

Rule-constrained scheduling has emerged as a widely adopted paradigm for residential appliance coordination, particularly under operational constraints and time-of-use (ToU) electricity tariffs. Recent studies have applied mixed-integer linear programming (MILP)~\cite{superchi2024optimization,masaud2024optimal}, cost-sensitive heuristic techniques~\cite{shui2024optimal,srivastava2024profit}, and rule-based control strategies~\cite{rambabu2024internet,jamal2024rule} to derive energy-efficient appliance operation schedules. While these approaches offer mathematically rigorous optimization under explicitly defined rule sets, they commonly assume that user constraints are provided in structured formats. As a result, they often lack flexibility in accommodating personalized preferences—especially those expressed in natural language—and typically do not incorporate behavior inference or adaptive constraint modeling derived from historical usage data. These limitations reduce their effectiveness in real-world residential scenarios, where user constraints are often implicit, dynamic, and semantically complex.

Concurrently, agent-based frameworks have attracted growing attention in broader energy management applications, including electric vehicle (EV) charging~\cite{wang2025analysing,wang2024scalable}, HVAC control~\cite{nguyen2024modelling,liu2024enhancing}, and demand response coordination~\cite{ajagekar2024energy,khodadadi2024data}. In some cases, household appliances are modeled as autonomous agents that interact with central controllers through negotiation protocols or local policy rules~\cite{dasgupta2025smart,rivkin2024aiot}. While these systems demonstrate potential for distributed coordination and adaptive scheduling, they often fall short in delivering cost-minimizing, constraint-compliant strategies that are grounded in appliance behavior and semantic user intent. To the best of our knowledge, no existing work has systematically integrated user preference interpretation, behavior-informed constraint generation, and tariff-aware optimization within a unified, agent-embedded scheduling module. Addressing this gap, we propose a \textit{Rule-Constrained Cost-Aware Scheduling} framework as the core decision-making engine within a personalized residential energy agent. This module bridges natural language instructions with executable scheduling plans by jointly modeling user constraints, behavior-derived operation windows, and dynamic tariff conditions. Compared to conventional rule-based or coordination-centric agent systems, our approach enables interpretable, adaptive, and deployable scheduling strategies that are more aligned with the realities of residential energy use.
\subsection{\textbf{LLMs for Semantic User Behavior Modeling}}

Large Language Models (LLMs) have demonstrated powerful capabilities in natural language understanding and task planning, offering new potential for user behavior modeling, preference interpretation, and constraint generation. Despite this, the application of LLMs in household energy management and scheduling is still in its early stages. Some recent works have explored their use in smart home command interpretation and energy-related question answering~\cite{giudici2024designing,michelon2025large,yonekura2024generating}, but few studies have systematically applied LLMs to appliance behavior recognition, scheduling constraint extraction, or semantic strategy modeling.

In contrast, our work is among the first to incorporate LLMs into the core modeling framework for residential energy optimization. Our system leverages LLMs to interpret appliance usage semantics and temporal patterns, constructing structured “start–run–stop” behavior units. It then automatically translates user preferences expressed in natural language into formal scheduling constraints. Built upon this semantic understanding, we develop a Rule-Constrained Cost-Aware Scheduling framework that generates cost-optimal, constraint-compliant schedules for flexible appliances. This unified architecture enhances system transparency, adaptability, and user alignment, offering a generalizable paradigm for intelligent energy agent design in residential settings.
\section{Methodology}
\label{sec:methodology}
This section systematically introduces the overall architecture and core technical components of our proposed intelligent residential energy agent. The system operates on minute-level appliance power time-series data, leveraging multi-layer semantic understanding and strategic optimization to construct a closed-loop agent that integrates user behavior modeling, constraint parsing, tariff adaptation, and scheduling optimization.

The overarching goal is to uncover energy-saving opportunities within daily electricity usage patterns under realistic household preferences and tariff constraints, to automatically generate interpretable and executable appliance operation schedules. In particular, we introduce a Large Language Model (LLM) to enhance the agent's task recognition and semantic structuring capabilities, and employ a rule-constrained cost-aware optimization module to produce actionable, personalized schedules that respect behavioral rules while minimizing energy cost.

\subsection{\textbf{Agent Architecture and Functional Modules}}
The proposed residential energy agent is built upon four fundamental capabilities of intelligent systems, including \textbf{perception}, \textbf{cognitive}, \textbf{reasoning}, and \textbf{optimization}, each embodied in a dedicated functional module. Rather than operating as a linear pipeline, these modules are integrated into a closed-loop architecture that continuously transforms high-frequency appliance-level power signals into structured behavioral insights and generates adaptive, cost-efficient operation schedules. Together, they represent distinct yet interconnected stages of a sensing-to-action workflow within the agent.

The \textbf{Perception Layer} performs
high-resolution data acquisition and preprocessing, ingesting raw appliance-level power signals sampled at high frequencies (e.g., every 8 seconds), filtering anomalies, and performing temporal alignment to produce structured multichannel time series with standardized temporal granularity of typically 1 minute. This resolution balances computational efficiency with behavioral fidelity, serving as the foundation for downstream event-based appliance usage modeling where individual device operations are segmented into distinct “start–run–stop” cycles.
Building upon this preprocessed data, the \textbf{Cognitive Layer}
Serving as the agent’s cognitive layer, this module transforms continuous power traces into semantically meaningful appliance usage events. It applies heuristic thresholds and leverages the structural reasoning capability of large language models (LLMs) to identify complete operational segments for each device. The resulting events are encoded into structured behavioral logs that capture when, how long, and how intensively each appliance is used. These logs are essential for understanding user habits and informing future scheduling strategies.
The \textbf{Reasoning Layer} bridges the critical gap between user intentions and formal optimization constraints by evaluating energy costs under various time-of-use (ToU) tariffs (e.g., Economy-7, Economy-10) and interpreting user preferences into formal scheduling constraints using LLMs. This dual-function design enables the module to reason externally about economic efficiency and internally about behavioral boundaries within a unified reasoning framework, effectively bridging economic evaluation and behavioral reasoning to support constraint-aware optimization.

Finally, the \textbf{Optimization Layer}
As the agent’s Optimization layer, this module generates optimized operation schedules for shiftable appliances by synthesizing behavioral logs and user-defined constraints. It adopts a rule-constrained, cost-aware optimization strategy that evaluates candidate execution windows based on minute-level energy cost and constraint feasibility. The system outputs include personalized scheduling recommendations, along with interpretable feedback on tariff savings, appliance behavior alignment, and user preference satisfaction.



These modules collectively enable a unified AI agent that links perception, behavior modeling, cost evaluation, and constraint-driven scheduling into a cohesive workflow, producing interpretable operation plans and personalized energy-saving strategies through a transparent and adaptive optimization loop that maintains both technical rigor and practical applicability in residential energy management scenarios.
\subsection{\textbf{Load Sensing and Alignment Module}}

\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/3.2.pdf}
\end{center}
\vspace{-10pt}
\caption{A unified temporal processing pipeline that transforms multi-resolution appliance-level data (e.g., REFIT: 8s, Smart Meters: 5min, CER: 15min) into behavior-preserving, aligned time series sequences for downstream modeling.}
\label{fig:3.2}
\end{figure*}

As the agent’s initial perception layer, this module addresses real-world data challenges—namely, heterogeneous sampling resolutions, inconsistent timestamp intervals, and signal-level anomalies—by transforming raw appliance-level power traces into temporally coherent and structurally aligned time series inputs. Rather than applying conventional interpolation or static resampling, the agent employs an adaptive temporal processing pipeline that preserves the semantic integrity of device behavior. As illustrated in Fig.~\ref{fig:3.2}, the full process consists of three key stages: (1) temporal consistency verification, (2) resolution alignment and aggregation, and (3) structured sequence construction.

\paragraph{\textbf{1) Temporal Detection and Consistency Verification}}

As shown in Step 1 of Fig.~\ref{fig:3.2}, the agent first ensures power consistency across all appliance channels by checking, at each time step \( t \), whether the total aggregated power \( P_{\text{agg}}(t) \) matches the sum of per-appliance power readings:

\begin{equation}
\left| P_{\text{agg}}(t) - \sum_{i=1}^{N} P_i(t) \right| > \epsilon
\label{eq:consistency}
\end{equation}

where \( \epsilon \) is a user-defined tolerance threshold (default: 2W). Records violating this criterion are flagged as anomalous unless explicitly preserved. In parallel, the agent verifies timestamp continuity; irregular or missing time intervals (e.g., \( \Delta t_t \neq \Delta t_{t-1} \)) trigger local interpolation or sequence segmentation to ensure downstream compatibility.

\paragraph{\textbf{2) Temporal Alignment and Adaptive Aggregation}}

Step 2 in Fig.~\ref{fig:3.2} illustrates how multiresolution data from sources such as REFIT (8s), Smart Meters (1–5min), or CER (15min) are aligned to a unified target resolution \( \Delta t_{\text{target}} \) (typically 1 minute). The alignment policy varies based on the raw sampling interval \( \Delta t_{\text{raw}} \) as follows:

When \( \Delta t_{\text{raw}} < \Delta t_{\text{target}} \) (e.g., 8s $\rightarrow$ 1min), a sliding time window \( W_t \) is used to compute the mean power within each minute:
   \begin{equation}
P_i^{(\text{agg})}(t) = \frac{1}{|W_t|} \sum_{t_k \in W_t} P_i(t_k)
\label{eq:aggregation}
\end{equation}

When \( \Delta t_{\text{raw}} = \Delta t_{\text{target}} \), no transformation is needed. Conversely, when \( \Delta t_{\text{raw}} > \Delta t_{\text{target}} \) (e.g., 15min $\rightarrow$ 1min), the agent applies linear interpolation, zero-order hold, or resolution-preserving processing based on appliance-specific configuration.

This adaptive strategy ensures that resolution normalization does not compromise the fidelity of behavioral signals. Both global defaults and per-appliance settings are supported for flexible deployment.

\paragraph{\textbf{3) Structured Sequence Construction and Output Buffering}}
In Step 3 of Fig.~\ref{fig:3.2}, the cleaned and aligned signals are organized into a unified multivariate time series matrix:

\begin{equation}
\mathbf{X} =
\begin{bmatrix}
t_1 & P_1(t_1) & \cdots & P_N(t_1) \\
t_2 & P_1(t_2) & \cdots & P_N(t_2) \\
\vdots & \vdots & \ddots & \vdots \\
t_T & P_1(t_T) & \cdots & P_N(t_T)
\end{bmatrix}
\label{eq:sequence}
\end{equation}

Each column corresponds to a specific appliance or the total load channel, while each row represents a normalized timestamp. This aligned time series buffer supports efficient semantic parsing, behavior segmentation, and downstream batch processing. The output can be exported in tabular (CSV), hierarchical (JSON), or database-compatible formats as illustrated in the right panel of Fig.~\ref{fig:3.2}.

All perception parameters—including consistency thresholds, resolution targets, and aggregation/interpolation strategies—are formally registered via JSON configuration files. This ensures full reproducibility, traceability, and scalable deployment across diverse household datasets.
Compared with conventional preprocessing pipelines, our approach emphasizes temporal adaptivity and behavioral fidelity, forming a critical foundation for semantically grounded modeling in later stages.
\subsection{\textbf{Appliance Behavior Modeling and Shiftability Identification}}
While appliance-level power signals provide high-frequency observations over time, they inherently lack semantic structure and cannot directly support downstream behavior modeling or scheduling optimization. This module enables the agent to perceive and abstract meaningful appliance operation cycles—defined as discrete “start–run–stop” segments—based on both power usage patterns and appliance semantics. 
As illustrated in Fig.~\ref{fig:3.3}, the full pipeline involves three key stages: (1) semantic reasoning and parameter initialization using a Large Language Model (LLM); (2) event segmentation via a double-threshold algorithm, and (3) shiftability classification and structured log generation.
\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/3.3.pdf}
\end{center}
\vspace{-10pt}
\caption{Semantic behavior modeling pipeline: LLM-based appliance inference (left), event segmentation based on power signals (center), and generation of shiftability-aware structured logs (right).}
\label{fig:3.3}
\end{figure*}
\textbf{{1) LLM-Based Appliance Semantic Inference and Parameter Initialization}}

Shown on the left side of Fig.~\ref{fig:3.3}, the agent first queries a pretrained LLM using the name of each appliance \( a_i \), to infer its functional characteristics and initialize event detection parameters. Each appliance is assigned a category label from the following taxonomy:
The taxonomy includes \textit{Shiftable loads} (e.g., washer, dryer, dishwasher) whose operation can be deferred without impacting functionality, \textit{Non-shiftable loads} (e.g., microwave, TV) that require immediate execution upon user demand, and \textit{Base loads} (e.g., refrigerator) that operate continuously or periodically without user control.

In addition to classification, the LLM provides prior knowledge on segmentation thresholds, including the minimum power \( P_{\text{min}}^{(i)} \) for distinguishing active versus idle states, and the minimum duration \( T_{\text{min}}^{(i)} \) for filtering out transient fluctuations. The full semantic inference process can be formalized as:
\begin{equation}
\text{LLM}(a_i) \rightarrow \left\{ \text{type}_i, P_{\text{min}}^{(i)}, T_{\text{min}}^{(i)} \right\}, \quad i = 1, 2, ..., N
\label{eq:llm_params}
\end{equation}

Compared to static rule-based methods, large language models (LLMs) offer semantic and contextual reasoning capabilities, enabling automated inference of appliance functionality and shiftability. This approach supports adaptive parameter selection for event segmentation, reducing manual tuning and improving generalizability. When available, historical data or user input can further refine these parameters for localized adaptation.

\textbf{{2) Appliance Operation Event Segmentation}}

As illustrated in the center of Fig.~\ref{fig:3.3}, each appliance’s power time series is segmented into discrete operation cycles using a double-threshold strategy. A valid segment is defined as a continuous period where the instantaneous power exceeds a device-specific minimum threshold and the duration surpasses a configured temporal window:

\begin{equation}
P_t > P_{\text{min}}, \quad \Delta t > T_{\text{min}}
\label{eq:segmentation}
\end{equation}

Each valid event is then encoded as a tuple containing start time, end time, and accumulated energy:

\begin{equation}
\left\langle t_{\text{start}}, t_{\text{end}}, E \right\rangle, \quad
E = \sum_{t = t_{\text{start}}}^{t_{\text{end}}} P_t \cdot \Delta t
\label{eq:event_energy}
\end{equation}

What distinguishes this segmentation process is its semantic grounding: the threshold parameters \( P_{\text{min}} \) and \( T_{\text{min}} \) are not fixed heuristics but dynamically initialized by the LLM-driven appliance reasoning module. This allows the agent to adjust its sensitivity based on the operational characteristics of each device, for example, applying stricter criteria for high-frequency appliances like kettles, and more lenient filters for slowly ramping devices like washing machines.

Moreover, the segmentation logic is embedded within the agent’s perception–reasoning loop. When ambiguous patterns arise (e.g., overlapping bursts or low-power intermittent usage), the agent can reevaluate segmentation boundaries by referencing historical logs, device metadata, or LLM-derived behavioral priors. This tight integration enables robust and semantically meaningful extraction of operation events, enhancing interpretability and accuracy in subsequent behavior modeling stages.
\textbf{{3) Shiftability Identification and Structured Log Generation}}

After extracting appliance operation segments, each event is annotated with its shiftability category based on the earlier classification stage. Events from '{shiftable appliances}' are forwarded to the scheduling optimizer, while those from '{non-shiftable}' or '{base loads}' are preserved solely for cost estimation and excluded from optimization.

To support downstream modeling, each valid operation is encoded into a structured log entry that captures its temporal and semantic attributes. Since appliances may operate multiple times per day, each entry is assigned a unique '{event\_id}' for disambiguation. The full log schema is presented in Table~\ref{tab:event_fields}.

\begin{table}[h]
\centering
\small
\caption{Structured fields of an appliance operation event}
\label{tab:event_fields}
\begin{tabular}{ll}
\toprule
\textbf{Field} & \textbf{Description} \\
\midrule
appliance\_name & Device identifier (e.g., \textit{Washer}) \\
event\_id & Unique index per operation event (e.g., Washer\_2024-06-01\_01) \\
shiftability & Device-level category (Shiftable / Non-shiftable / Base) \\
start\_time, end\_time & Timestamps of the operation segment \\
duration & Total operation time in minutes \\
power\_trace & Raw power values during this event (1-min resolution) \\
energy & Accumulated energy over the segment ($E = \sum P_t \cdot \Delta t$) \\
is\_reschedulable & Boolean flag indicating if this event is adjustable \\
\bottomrule
\end{tabular}
\end{table}

This standardized representation serves multiple purposes. It abstracts low-level traces into interpretable usage units for behavior modeling, while the '{is\_reschedulable}' flag allows fine-grained control at the event level, accounting for factors such as minimal duration or late-night usage that may render certain segments non-adjustable. All logs are stored in structured formats (e.g., JSON or DataFrames) and passed to the scheduling module, where tariff conditions and user-defined constraints guide the generation of optimized operation plans.
\subsection{\textbf{Tariff Modeling and Constraint Parsing}}

As part of the agent’s semantic reasoning layer, this module translates real-world user expectations into formal scheduling constraints. It jointly considers electricity tariffs and natural language preferences to produce appliance-specific constraint profiles.

Effective coordination in residential energy systems requires balancing cost efficiency with user-specific routines—such as avoiding noise at night or meeting task deadlines. As shown in Fig.~\ref{fig:3.4.1} and Fig.~\ref{fig:3.4.2}, this module integrates economic signals and behavioral semantics into a unified constraint representation, serving as the foundation for subsequent scheduling optimization.

\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/3.4.1.pdf}
\end{center}
\vspace{-10pt}
\caption{Left: Detected appliance working events and associated Time-of-Use (ToU) tariffs. Right: Cost comparison under different tariff policies and AI agent's recommended strategy.}
\label{fig:3.4.1}
\end{figure*}

\textbf{{1) Tariff-Aware Cost Modeling and Strategy Recommendation}}

The agent incorporates multiple time-of-use (ToU) electricity pricing schemes, such as the UK’s Standard, Economy 7, and Economy 10 tariffs, each encoded as a time-dependent pricing function \( \text{Price}_k(t) \). These policies define distinct off-peak periods, either as single windows (Economy 7) or multiple daily intervals (Economy 10). Fig.~\ref{fig:3.4.1} (bottom left) visualizes their temporal pricing structures.

Given appliance-level operation logs, the agent simulates daily cost under each tariff \( \mathcal{T}_k \) using:

\begin{equation}
\text{Cost}^{(k)}_{\text{total}} = \sum_{d \in \mathcal{D}} \sum_{a \in \mathcal{A}} \sum_{t \in T_{\text{run}}(a, d)} P_{a,t} \cdot \Delta t \cdot \text{Price}_k(t)
\label{eq:tariff_cost}
\end{equation}

The comparison results (Fig.~\ref{fig:3.4.1}, right) enable the agent to recommend a personalized tariff strategy based on actual behavior. This recommendation adapts dynamically to season, household profile, and appliance types, supporting contract choice or real-time response plans.

\textbf{{2) LLM-Based User Preference Interpretation and Constraint Structuring}}

Beyond tariff modeling, effective appliance scheduling must respect user-defined behavioral constraints. This module interprets natural language preferences, such as time-of-day restrictions or completion deadlines, into formal, time-resolved masks that specify when each appliance is permitted to operate. These constraints define the feasible scheduling space and are essential for aligning optimization with user intent. As illustrated in Fig.~\ref{fig:3.4.2} (left), users may express expectations in intuitive forms such as:
\vspace{-3pt}
\begin{center}
\textit{“I don’t want the dryer to run at night.”} \\
\textit{“Washer must finish before next day 12:00.”}
\end{center}
\vspace{-5pt}
\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/3.4.2.pdf}
\end{center}
\vspace{-10pt}
\caption{Left: LLMs interpret user instructions into structured constraints. Right: appliance-level constraint segmentation and output for downstream scheduling.}
\label{fig:3.4.2}
\end{figure*}
To interpret these preferences, the agent employs a large language model (LLM) for semantic parsing. Each instruction is mapped to a structured constraint object, including appliance identifiers, constraint types (e.g., {forbidden\_interval}, {latest\_finish}, {shift\_rule}), and specific time references, like Table~\ref{tab:user_constraints_example}. For example: The LLM not only classifies constraint types but also resolves ambiguous expressions (e.g., “before I leave”) using contextual metadata (e.g., departure time = 08:30). When overlapping or conflicting constraints are detected (e.g., “forbid after 23:00” vs. “finish by 24:00”), the agent applies rule prioritization or prompts clarification to ensure semantic consistency.

\begin{table}[h]
\centering
\small  
\caption{Structured representation of user-defined scheduling constraints}
\label{tab:user_constraints_example}
\begin{tabular}{lll}
\toprule
\textbf{Appliance} & \textbf{Constraint Type} & \textbf{Time Interval} \\
\midrule
Dryer     & forbidden\_interval  & 23:00–30:30     \\
Washer    & latest\_finish       & 30:00           \\
Washer    & shift\_rule          & only\_delay     \\
\bottomrule
\end{tabular}
\end{table}

Once extracted, these constraints are used to generate binary time masks (Allow / Forbid) for each appliance. The agent first derives a maximum schedulable window for each device based on the ‘{latest\_finish}’ constraint. To accommodate cross-day operations, all time values are normalized to a 0–48 hour format (e.g., 30:00 = 06:00 on the second day). Then, any daily recurring ‘{forbidden\_intervals}’ such as 23:00–06:30 are subtracted to form the final legal operation windows. Each detected usage event is then evaluated against these time masks to determine whether it is ‘{reschedulable}’. Only those fully falling within allowed regions are passed to the scheduler for optimization. If a ‘{shift\_rule}’ such as ‘{only\_delay}’ is specified, earlier start times than the original event are additionally excluded.

The resulting constraints and event-level migration masks are exported in structured formats (CSV or JSON) for downstream scheduling. These rule-structured constraints support the downstream \textit{Rule-Constrained Cost-Aware Scheduling} module in determining optimal runtime slots under realistic behavioral boundaries.

For reproducibility and transparency, the exact LLM configuration used in our system employs GPT-4 (OpenAI) accessed via API with maximum tokens = 1024, temperature = 0.7, and timeout = 60 seconds, utilizing two primary prompt templates:

\textit{Appliance Parameter Inference Prompt:} For each appliance name $a_i$, the LLM receives a structured prompt requesting: (1) English standardized name, (2) shiftability classification (Shiftable/Non-shiftable/Base), (3) minimum power threshold $P_{\text{min}}$, and (4) minimum duration $T_{\text{min}}$. The prompt includes examples and explicit output format requirements to ensure consistent JSON responses.

\textit{Constraint Parsing Prompt:} User natural language instructions are processed using a template that maps common constraint expressions (e.g., "avoid running at night", "finish before noon") to structured constraint objects with fields: appliance\_name, constraint\_type (forbidden\_interval, latest\_finish, shift\_rule), and time\_interval.

To handle ambiguous inputs, the system implements a fallback mechanism: when JSON parsing fails or constraints conflict, the agent queries the user for clarification through follow-up prompts. The system maintains a constraint priority hierarchy where safety-related restrictions (e.g., forbidden\_interval) override convenience preferences (e.g., preferred\_time).

The LLM constraint parsing process can be formalized as a mapping function:

\begin{equation}
\mathcal{M}: \mathcal{L} \rightarrow \mathcal{C}
\label{eq:llm_mapping}
\end{equation}

where $\mathcal{L}$ represents the space of natural language expressions and $\mathcal{C}$ represents the structured constraint space. Each constraint $c \in \mathcal{C}$ is a tuple $(a, type, params)$ where $a$ is the appliance identifier, $type \in \{forbidden, deadline, preference\}$, and $params$ contains time-specific parameters.

When multiple constraints conflict, the system applies a resolution function:

\begin{equation}
\text{Resolve}(\{c_1, c_2, ..., c_k\}) = \arg \max_{c_i} \rho(c_i)
\label{eq:conflict_resolution}
\end{equation}

where $\rho(c_i)$ represents the priority weight of constraint $c_i$. The priority hierarchy follows: $\rho(forbidden) > \rho(deadline) > \rho(preference)$ with numerical values $\{1.0, 0.7, 0.3\}$ respectively.

\begin{algorithm}[H]
\caption{LLM-Based Constraint Parsing Algorithm}
\label{alg:constraint_parsing}
\begin{algorithmic}[1]
\REQUIRE Natural language input $L$, appliance list $\mathcal{A}$
\ENSURE Structured constraint set $\mathcal{C}$
\STATE Initialize $\mathcal{C} \leftarrow \emptyset$
\STATE $tokens \leftarrow \text{Tokenize}(L)$
\STATE $prompt \leftarrow \text{BuildPrompt}(tokens, \mathcal{A})$
\STATE $response \leftarrow \text{LLM}(prompt)$
\STATE $parsed \leftarrow \text{ParseJSON}(response)$
\FOR{each constraint $c$ in $parsed$}
    \IF{$\text{ValidateConstraint}(c)$}
        \STATE $\mathcal{C} \leftarrow \mathcal{C} \cup \{c\}$
    \ELSE
        \STATE $clarification \leftarrow \text{RequestClarification}(c)$
        \STATE $c_{corrected} \leftarrow \text{LLM}(clarification)$
        \STATE $\mathcal{C} \leftarrow \mathcal{C} \cup \{c_{corrected}\}$
    \ENDIF
\ENDFOR
\STATE $\mathcal{C}_{resolved} \leftarrow \text{ResolveConflicts}(\mathcal{C})$
\RETURN $\mathcal{C}_{resolved}$
\end{algorithmic}
\end{algorithm}
\textbf{{3) Integrated Reasoning for Feasibility-Aware Scheduling}}

This module produces two key outputs: tariff-informed cost models for pricing evaluation and structured, appliance-level constraint sets derived from user preferences. Together, these define the feasible scheduling space for downstream optimization.

By jointly considering external tariff structures and internal behavioral constraints, the agent eliminates infeasible schedules and steers the optimization toward solutions that are cost-efficient, constraint-compliant, and aligned with user routines. This structured transition from semantic interpretation to executable planning enhances transparency and ensures practical applicability in real-world residential settings.

\subsection{\textbf{Scheduling Optimization and Adaptive Recommendation}}

\textbf{{1) Rule-Constrained Optimization Strategy}}
\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/3.5.pdf}
\end{center}
\vspace{-10pt}
\caption{Top: Agent receives time-of-use tariffs and constraint inputs. Middle: Optimization flow based on rule-constrained heuristic scheduling strategy; results are evaluated across candidate time windows under ToU tariffs. Bottom: Tabular output of recommended schedules and tariff evaluation.}
\label{fig:3.5}
\end{figure*}

This module serves as the agent’s core decision-making engine, responsible for generating interpretable and cost-efficient operation schedules for shiftable appliances under user-defined constraints and dynamic pricing schemes. The scheduling process is built upon a rule-constrained heuristic strategy that balances behavior-awareness, tariff sensitivity, and constraint compliance.

The residential energy scheduling problem can be formulated as a constrained optimization task where $\mathcal{E} = \{e_1, e_2, ..., e_n\}$ denotes the set of shiftable appliance events, with each event $e_i$ characterized by duration $d_i$, power profile $P_i(t)$, and original start time $t_i^{orig}$. The objective is to find optimal start times $\mathbf{t}^* = \{t_1^*, t_2^*, ..., t_n^*\}$ that minimize total electricity cost:

\begin{equation}
\mathbf{t}^* = \arg \min_{\mathbf{t}} \sum_{i=1}^{n} \sum_{k=0}^{d_i} P_i(k) \cdot \text{Price}(t_i + k) \cdot \Delta t
\label{eq:optimization_objective}
\end{equation}

subject to user-defined constraints:
\begin{align}
t_i + d_i &\leq T_i^{deadline} \quad \forall i \in \mathcal{E} \label{eq:deadline_constraint} \\
t_i &\notin \mathcal{F}_i \quad \forall i \in \mathcal{E} \label{eq:forbidden_constraint} \\
|t_i - t_j| &\geq \delta_{ij} \quad \forall i,j \text{ with appliance conflicts} \label{eq:conflict_constraint}
\end{align}

where $T_i^{deadline}$ represents the latest acceptable completion time, $\mathcal{F}_i$ denotes forbidden time intervals, and $\delta_{ij}$ specifies minimum separation between conflicting appliances.

The electricity pricing function $\text{Price}(t)$ is defined as a piecewise function based on the selected tariff scheme:

\begin{equation}
\text{Price}(t) = \begin{cases}
P_{low} & \text{if } t \in \mathcal{T}_{low} \\
P_{high} & \text{if } t \in \mathcal{T}_{high}
\end{cases}
\label{eq:tariff_function}
\end{equation}

where $\mathcal{T}_{low}$ and $\mathcal{T}_{high}$ represent the low-rate and high-rate time periods respectively, with $P_{low} < P_{high}$. For Economy-7: $\mathcal{T}_{low} = [00:00, 07:00]$, and for Economy-10: $\mathcal{T}_{low} = [01:00, 06:00] \cup [13:00, 16:00] \cup [20:00, 22:00]$.

User preferences are mathematically represented as constraint sets, with the following definitions for each appliance $i$:

\begin{align}
\mathcal{C}_i^{forbidden} &= \{t : t \in \mathcal{F}_i\} \label{eq:forbidden_set} \\
\mathcal{C}_i^{deadline} &= \{t : t + d_i \leq T_i^{deadline}\} \label{eq:deadline_set} \\
\mathcal{C}_i^{feasible} &= \mathcal{C}_i^{deadline} \setminus \mathcal{C}_i^{forbidden} \label{eq:feasible_set}
\end{align}

The feasible scheduling space for event $i$ is the intersection of all applicable constraints: $\mathcal{W}_i = \mathcal{C}_i^{feasible} \cap \mathcal{T}_{available}$, where $\mathcal{T}_{available}$ represents time slots not occupied by other scheduled events.

Based on structured event logs and constraint masks derived from earlier modules, the agent first evaluates the reschedulability of each appliance event. Only those whose original time slots fully fall within the allowable range—after considering ‘{forbidden\_interval}’, ‘{latest\_finish}’, and ‘{shift\_rule}’ are marked as ‘{reschedulable}’.

For each ‘{reschedulable}’ event, the agent enumerates feasible execution windows \( \mathcal{W}_i \) by intersecting:
This intersection considers both the event's allowed operation range derived from constraints and the tariff-specific off-peak periods (e.g., low-cost intervals under Economy 7 or Economy 10).

This intersection produces a set of cost-aware migration zones that respect both user behavior boundaries and economic incentives. Within each feasible sub-window, the agent estimates the electricity cost by applying minute-level power traces and corresponding ToU prices. The cost estimation for any candidate start time \( t_s \) is computed as follows, where \( \Delta t_i \) denotes the event’s duration, \( \delta \) is the time resolution (typically 1 minute), and \( \text{Price}(t) \) refers to the electricity tariff at time \( t \).

\begin{equation}
\text{Cost}_i(t_s) = \sum_{k=0}^{\Delta t_i / \delta} P_i(t_s + k \cdot \delta) \cdot \delta \cdot \text{Price}(t_s + k \cdot \delta)
\label{eq:cost_estimation}
\end{equation}
Among all time slots that satisfy behavioral and tariff constraints, the optimal migration point is selected according to:

\begin{equation}
t_i^* = \arg \min_{t_s \in \mathcal{W}_i} \text{Cost}_i(t_s)
\label{eq:schedule_selection}
\end{equation}

This rule-constrained, cost-aware scheduling strategy is independently applied to each shiftable appliance and its corresponding operation events. The algorithm follows a greedy approach with the following precedence rules: (1) events are processed in chronological order of their original start times; (2) for each event, the algorithm selects the earliest available time slot within the feasible window that minimizes cost; (3) once scheduled, each event reserves its time slot to prevent overlaps.

When multiple events compete for the same optimal time slot, the system applies a priority-based resolution mechanism that prioritizes events based on user-specified deadlines (earliest deadline first), energy consumption (higher consumption events prioritized to maximize savings), and appliance type (critical appliances like washing machines prioritized over discretionary ones). If conflicts cannot be resolved within the feasible windows, the conflicting events retain their original scheduling times.

While this approach does not guarantee global optimality across all appliances simultaneously, it ensures strong interpretability, computational scalability, and adherence to user-defined constraints—attributes essential for practical deployment in residential energy systems. The greedy strategy achieves near-optimal results for most households while maintaining computational efficiency suitable for real-time operation.

\begin{algorithm}[H]
\caption{Rule-Constrained Cost-Aware Scheduling Algorithm}
\label{alg:scheduling}
\begin{algorithmic}[1]
\REQUIRE Event set $\mathcal{E}$, tariff function $\text{Price}(t)$, constraint sets $\{\mathcal{C}_i\}$
\ENSURE Optimized schedule $\mathbf{t}^* = \{t_1^*, t_2^*, ..., t_n^*\}$
\STATE Initialize $\mathbf{t}^* \leftarrow \emptyset$, $\mathcal{T}_{occupied} \leftarrow \emptyset$
\STATE Sort events by priority: $\mathcal{E}_{sorted} \leftarrow \text{SortByPriority}(\mathcal{E})$
\FOR{each event $e_i \in \mathcal{E}_{sorted}$}
    \STATE Compute feasible window: $\mathcal{W}_i \leftarrow \mathcal{C}_i^{feasible} \setminus \mathcal{T}_{occupied}$
    \IF{$\mathcal{W}_i = \emptyset$}
        \STATE $t_i^* \leftarrow t_i^{orig}$ \COMMENT{Keep original time if no feasible window}
    \ELSE
        \STATE $t_i^* \leftarrow \arg \min_{t \in \mathcal{W}_i} \sum_{k=0}^{d_i} P_i(k) \cdot \text{Price}(t + k)$
        \STATE Update occupied slots: $\mathcal{T}_{occupied} \leftarrow \mathcal{T}_{occupied} \cup [t_i^*, t_i^* + d_i]$
    \ENDIF
    \STATE Add to schedule: $\mathbf{t}^* \leftarrow \mathbf{t}^* \cup \{t_i^*\}$
\ENDFOR
\RETURN $\mathbf{t}^*$
\end{algorithmic}
\end{algorithm}

The event priority function $\pi(e_i)$ combines multiple factors:

\begin{equation}
\pi(e_i) = w_1 \cdot \frac{1}{T_i^{deadline} - t_i^{orig}} + w_2 \cdot \frac{E_i}{E_{max}} + w_3 \cdot \alpha_i
\label{eq:priority_function}
\end{equation}

where $w_1, w_2, w_3$ are weighting factors (default: 0.5, 0.3, 0.2), $E_i$ is the event's energy consumption, $E_{max}$ is the maximum energy among all events, and $\alpha_i$ is an appliance-type priority coefficient (washing machine: 1.0, dryer: 0.8, dishwasher: 0.6).

The potential cost savings for shifting an event from time $t_{orig}$ to $t_{new}$ can be quantified as:

\begin{equation}
\Delta C_i = \sum_{k=0}^{d_i} P_i(k) \cdot [\text{Price}(t_{orig} + k) - \text{Price}(t_{new} + k)] \cdot \Delta t
\label{eq:cost_savings}
\end{equation}

The maximum theoretical savings occurs when an event is shifted from peak to off-peak periods entirely:

\begin{equation}
\Delta C_i^{max} = E_i \cdot (P_{high} - P_{low}) = E_i \cdot \Delta P
\label{eq:max_savings}
\end{equation}

where $\Delta P = P_{high} - P_{low}$ represents the tariff differential. For UK Economy-7, $\Delta P = 0.15$ GBP/kWh, providing substantial savings potential for high-energy appliances.

When constraints cannot be satisfied, the system defines a penalty function:

\begin{equation}
\text{Penalty}_i = \begin{cases}
\infty & \text{if hard constraint violated} \\
\beta \cdot |t_i^* - t_i^{preferred}| & \text{if soft constraint violated}
\end{cases}
\label{eq:penalty_function}
\end{equation}

where $\beta$ is a penalty coefficient and $t_i^{preferred}$ represents the user's preferred time when available.

The proposed algorithm exhibits polynomial time complexity, with the following analysis for $n$ events, $m$ time slots, and $k$ constraints per event:

\begin{align}
T_{sorting} &= O(n \log n) \label{eq:sort_complexity} \\
T_{scheduling} &= O(n \cdot m \cdot k) \label{eq:schedule_complexity} \\
T_{total} &= O(n \log n + n \cdot m \cdot k) = O(n \cdot m \cdot k) \label{eq:total_complexity}
\end{align}

The space complexity is $O(n + m)$ for storing events and time slot availability. This linear scaling with household size makes the algorithm suitable for real-time deployment in residential energy management systems, unlike exponential MILP formulations that become intractable for large-scale applications.

While the greedy approach does not guarantee global optimality, it provides a bounded approximation that achieves optimal cost reduction when all events can be shifted to off-peak periods without conflicts. The approximation ratio can be bounded as:

\begin{equation}
\frac{C_{greedy}}{C_{optimal}} \leq 1 + \frac{\max_i \Delta C_i}{\sum_i \Delta C_i}
\label{eq:approximation_ratio}
\end{equation}

where $\Delta C_i$ represents the cost savings for event $i$. In practice, this ratio remains close to 1.0 for most residential scenarios due to the relatively small number of conflicting events.

The finalized schedules are exported in structured formats (e.g., JSON, CSV) and can be seamlessly integrated into user interfaces or smart controllers. As illustrated in Fig.~\ref{fig:3.5}, the system provides clear runtime recommendations, projected cost savings, and tariff selection guidance, thereby supporting users in adopting economically efficient and behaviorally aligned appliance schedules.
{\textbf{2) Structured Output and Recommendation Presentation}}

The scheduling module generates a set of optimized operation plans for all shiftable appliances, represented as:
\begin{equation}
\mathcal{T}_{\text{schedule}} = \{ (a_i, t_i^*) \}, \quad \forall a_i \in \mathcal{A}_{\text{shiftable}}
\end{equation}
where $a_i$ denotes a shiftable device and $t_i^*$ its selected start time determined by Equation~\ref{eq:schedule_selection}.

As shown in Fig.~\ref{fig:3.5} (bottom), the output includes a comparative overview of each appliance’s original and optimized operation windows, along with the corresponding energy costs under the applied tariff model. For each device, the system reports: (i) the recommended start and end times, (ii) compliance with user-defined constraints, and (iii) the estimated electricity cost.

The results are presented in both visual and tabular formats to support user interpretation, and exported as machine-readable files (e.g., CSV, JSON) for integration with external interfaces such as energy dashboards or smart controllers. In addition to per-device scheduling plans, the system also performs cost comparisons across tariff options (e.g., Economy~7 vs.\ Economy~10), helping users identify the most economical pricing scheme based on actual usage.

By translating high-level semantic reasoning into concrete and interpretable execution plans, this module closes the loop between behavioral modeling and actionable control. The proposed scheduling strategy combines efficiency, transparency, and compatibility with real-world deployment requirements in residential energy systems.
\section{Experiments}
\label{sec:experiments}
This section systematically evaluates the performance of the proposed personalized energy scheduling agent in real-world household settings. The experiments focus on three aspects: (1) cost-saving performance under different time-of-use (ToU) tariffs; (2) identification and filtering of shiftable appliance events; and (3) success rate of schedule migration and cost reduction under user constraints.

\subsection{\textbf{Experimental Setup}}

We conduct experiments on the publicly available REFIT dataset, one of the most comprehensive repositories of residential electricity usage in the UK. It comprises 8-second resolution power readings for 20 households over a two-year period, covering nine commonly used appliances (e.g., fridge, washing machine, dryer, computer). In total, the dataset contains over 1.19 billion measurements and more than 250,000 recorded appliance usage events.

Our proposed agent-based system performs a full end-to-end scheduling pipeline for each household, including data preprocessing, appliance labeling, event segmentation, user constraint parsing, and scheduling optimization. To support semantic generalization and domain reasoning, we utilize two structured knowledge bases:
The system utilizes two structured knowledge bases: an \textbf{Appliance Scheduling Dictionary} that defines shiftability (Shiftable, Non-shiftable, or Base) for 280 appliance categories along with typical operational patterns, and an \textbf{Appliance Threshold Dictionary} that specifies minimum power ($P_\mathrm{min}$) and duration ($T_\mathrm{min}$) thresholds to identify valid operating events, with the system leveraging an LLM to infer attributes for appliances not present in the dictionary.

To assess the system's responsiveness to time-varying electricity costs, we model three common Time-of-Use (ToU) pricing schemes in the UK residential context. The detailed configurations are summarized in Table~\ref{tab:tariff-config}.

\begin{table}[H]
\centering
\small
\caption{ToU Tariff Configurations}
\label{tab:tariff-config}
\begin{tabular}{lll}
\toprule
\textbf{Tariff} & \textbf{Low-Rate Period(s)} & \textbf{High-Rate Period(s)} \\
\midrule
Standard   & None (Flat rate)                  & All day (£0.30/kWh) \\
Economy 7  & 00:00–07:00 (£0.15/kWh)           & 07:00–24:00 (£0.30/kWh) \\
Economy 10 & 01:00–06:00, 13:00–16:00, 20:00–22:00 (£0.15/kWh) & Other hours (£0.30/kWh) \\
\bottomrule
\end{tabular}
\end{table}
Each tariff scheme is evaluated under two scheduling scenarios. In the \textit{Original} mode, all appliance events retain their original execution times without adjustment. In contrast, the \textit{Optimized} mode allows eligible events to be rescheduled into lower-cost periods, subject to user-defined constraints. To ensure a fair comparison, the total energy consumption per event remains unchanged across modes, allowing cost differences to reflect only the impact of scheduling decisions.

To ensure a fair comparison, all scheduling plans preserve the original energy consumption of each appliance. Thus, cost differences arise solely from changes in event timing under different tariff structures.
\subsection{\textbf{Shiftable Event Identification and Filtering}}

To ensure meaningful and controllable scheduling inputs, the system implements a structured pipeline for identifying valid and cost-relevant operation events from raw appliance power traces. This component serves as a crucial preprocessing stage that bridges real-world appliance usage patterns with optimization-ready scheduling units. The goal is to extract, classify, and validate events that are both technically schedulable and behaviorally acceptable, thus enabling robust and constraint-compliant optimization in later stages. The full pipeline consists of five steps:

The pipeline begins with appliance annotation and initialization, where appliance types are identified based on user-provided descriptions and each device is mapped to a predefined appliance dictionary containing typical scheduling parameters such as shiftability class, minimum power threshold $P_\text{min}$, and minimum duration $T_\text{min}$, with an LLM-based semantic parser inferring default properties for unknown devices to ensure complete initialization.

Subsequently, the system performs event segmentation via behavior modeling, where
Given the initialized parameters, the system segments continuous power traces into discrete usage events. A dual-threshold rule extracts operational periods exceeding both $P_\text{min}$ and $T_\text{min}$, capturing full “start–run–stop” cycles. Each event is stored with duration, energy, and timestamp metadata.

\textbf{3) Shiftability-Based Filtering:}
Extracted events are filtered according to appliance-level shiftability labels. Events from \textit{Shiftable} appliances are marked as reschedulable, while those from \textit{Non-shiftable} and \textit{Base} loads are excluded from scheduling and retained only for cost analysis.

\textbf{4) Constraint Compliance Check:}
For all potentially reschedulable events, the system validates compliance with user-defined temporal preferences parsed by LLMs. These constraints are translated into minute-level time masks, and any event violating them is excluded from scheduling consideration.

\textbf{5) Tariff-Aware Refinement:}
Finally, the event set is refined based on the selected tariff model. Events already aligned with low-cost periods or with negligible migration benefit (e.g., short duration during off-peak) are filtered out. When multiple events occur within a day, precedence and overlap prevention are also enforced.
\begin{table}[htbp]
\small
\centering
\caption{Shifted Events Summary under Tariff Schemes}
\label{tab:shifted_events_summary}
\begin{tabular}{c|c|c|cc|cc}
\toprule
\textbf{House} & \textbf{Total Events} & \textbf{min\_duration} &
\multicolumn{2}{c|}{\textbf{Shifted Events}} &
\multicolumn{2}{c}{\textbf{Success Events}} \\
\cmidrule(lr){4-5} \cmidrule(lr){6-7}
& (Pmin) & Filtered & Tariff 7 & Tariff 10 & Tariff 7 & Tariff 10 \\
\midrule
1  & 1820  & 1654  & 1433 & 1180 & 1433 & 1176 \\
2  & 1776  & 840   & 819  & 731  & 817  & 729  \\
3  & 2672  & 1904  & 1848 & 1540 & 1846 & 1538 \\
4  & 960   & 599   & 564  & 575  & 562  & 574  \\
5  & 5748  & 1863  & 1236 & 1084 & 1176 & 975  \\
6  & 528   & 261   & 258  & 223  & 257  & 222  \\
7  & 3447  & 2489  & 2476 & 2007 & 2473 & 2004 \\
8  & 1142  & 1045  & 239  & 377  & 153  & 233  \\
9  & 13414 & 12912 & 8632 & 7282 & 234  & 364  \\
10 & 2439  & 1391  & 1303 & 1118 & 1302 & 1118 \\
11 & 1221  & 235   & 220  & 161  & 220  & 161  \\
12 & --    & --    & --   & --   & --   & --   \\
13 & 3496  & 809   & 797  & 698  & 790  & 689  \\
14 & --    & --    & --   & --   & --   & --   \\
15 & 701   & 409   & 353  & 313  & 353  & 313  \\
16 & 439   & 258   & 240  & 203  & 237  & 183  \\
17 & 859   & 431   & 418  & 333  & 418  & 332  \\
18 & 756   & 491   & 428  & 349  & 395  & 331  \\
19 & 10432 & 10144 & 7112 & 6054 & 7057 & 6017 \\
20 & 1015  & 593   & 587  & 518  & 587  & 517  \\
21 & 1327  & 1000  & 773  & 660  & 771  & 658  \\
\bottomrule
\end{tabular}
\end{table}

Building upon the five-stage procedure introduced above, we now examine how the proposed event filtering pipeline performs across real-world household data. Specifically, we aim to assess whether the system can robustly extract a meaningful and constraint-compliant set of schedulable events from diverse usage patterns, and whether the resulting input supports reasonable scheduling success rates under realistic tariff conditions.

Table~\ref{tab:shifted_events_summary} summarizes the results for 20 households in the REFIT dataset. It reports, for each household: (i) the total number of raw appliance operation events; (ii) the number retained after applying duration and power thresholds (\textit{Events after minimum duration/power filtering}); (iii) the subset identified as eligible for rescheduling under Economy~7 and Economy~10 (\textit{Eligible Events for Rescheduling}); and (iv) the number of events successfully rescheduled without constraint violations (\textit{Successfully Rescheduled Events}).

The data show that the multi-stage pipeline effectively reduces noise while preserving actionable events. For example, House~9 initially generated over 13{,}000 raw events, from which more than 8{,}600 were identified as eligible for rescheduling under Economy~7. However, the final success rates vary significantly across households, revealing important system limitations.

\textbf{Critical Analysis of Scheduling Success Rates:} The comparison between "Eligible Events" and "Successfully Rescheduled Events" reveals substantial variations in actual rescheduling performance. While some households (e.g., Houses 1, 3, 7) achieve near-complete success rates (>95\%), others show significant drops. Most notably, House~9 exhibits only 234 successful reschedules out of 8{,}632 eligible events under Economy~7 (2.7\% success rate), and House~5 shows 1{,}176 successes from 1{,}236 eligible events (95.1\% success rate).

These variations stem from three primary factors: (1) \textit{Constraint conflicts} where user-defined time restrictions (e.g., no operation after 23:00) conflict with tariff-optimal windows; (2) \textit{Scheduling window limitations} where the available off-peak periods are insufficient to accommodate all eligible events while maintaining minimum inter-event spacing; and (3) \textit{Appliance-specific restrictions} where certain device types have inherently limited flexibility despite being classified as "shiftable."

The comparison between tariff models further reveals the impact of pricing structures. While Economy~7 provides a single overnight off-peak window, Economy~10’s multiple low-cost periods enable greater temporal flexibility and reduce scheduling conflicts. This advantage is reflected in higher success rates in households such as House~5 and House~3, despite comparable or smaller initial event pools.

Two households were excluded due to incomplete data. House~12 lacked valid appliance annotations, and House~14 contained corrupted power readings. These exceptions were automatically detected and omitted without disrupting the analysis.

In summary, the experimental results validate both the accuracy of the event representation framework and the high completion rate achieved by the agent under real-world conditions. The system reliably identifies and schedules meaningful appliance activities across diverse usage patterns and constraints, establishing a strong foundation for subsequent cost-aware optimization.
\subsection{\textbf{Cost Evaluation under Original and Optimized Scheduling}}
Following the identification of '{reschedulable}' events, we assess the agent's ability to reduce electricity expenditures through optimized scheduling under three UK Time-of-Use (ToU) tariff schemes: Standard (flat rate), Economy-7 (overnight off-peak), and Economy-10 (multi-period off-peak).  For each household, the system performs cost estimation before and after optimization using consistent power traces, allowing a fair evaluation of time-shifting impacts. To quantify scheduling effectiveness, we calculate comprehensive energy system performance metrics:

The economic performance metrics include:
\begin{align}
\text{Absolute Savings} &= C_{original} - C_{optimized} \label{eq:abs_savings} \\
\text{Savings Rate} &= \frac{C_{original} - C_{optimized}}{C_{original}} \times 100\% \label{eq:savings_rate} \\
\text{Cost per kWh Reduction} &= \frac{\text{Absolute Savings}}{E_{total}} \label{eq:cost_per_kwh}
\end{align}

The operational performance metrics include:
\begin{align}
\text{Scheduling Success Rate} &= \frac{|\text{Successfully Rescheduled Events}|}{|\text{Eligible Events}|} \times 100\% \label{eq:success_rate} \\
\text{Constraint Compliance} &= \frac{|\text{Constraint-Compliant Events}|}{|\text{Total Events}|} \times 100\% \label{eq:compliance_rate} \\
\text{Peak Load Reduction} &= \frac{P_{peak,original} - P_{peak,optimized}}{P_{peak,original}} \times 100\% \label{eq:peak_reduction}
\end{align}

The energy system impact metrics include:
\begin{align}
\text{Load Factor Improvement} &= \frac{LF_{optimized} - LF_{original}}{LF_{original}} \times 100\% \label{eq:load_factor} \\
\text{Demand Response Potential} &= \frac{E_{shifted}}{E_{total}} \times 100\% \label{eq:dr_potential}
\end{align}

where $LF = \frac{P_{avg}}{P_{peak}}$ represents the load factor, and $E_{shifted}$ is the total energy successfully moved to off-peak periods.
As shown in Table~\ref{tab:cost_table}, the proposed agent consistently reduces energy costs under both Economy~7 and Economy~10. For instance, House~1’s bill drops from £623.99 (Standard) to £395.51 after optimization under Economy~10, while House~17 achieves a reduction from £513.55 to £381.33. These improvements are directly attributable to the successful migration of events into lower-priced time windows, while strictly adhering to user-defined constraints.

Across the dataset, Economy~10 demonstrates superior savings potential. Its three distributed off-peak periods offer greater temporal flexibility compared to the single overnight window in Economy~7, enabling more events to be legally rescheduled. In high-activity households such as House~5 and House~19, the agent successfully migrates thousands of events while maintaining behavioral validity—leading to savings exceeding 20\%.

The relationship between behavioral flexibility and economic outcome is further corroborated by Table~\ref{tab:shifted_events_summary}. Households with a higher volume of successfully rescheduled events (e.g., House~7 and House~19 with over 7,000 shifts) exhibit correspondingly higher cost reductions. In contrast, households with fewer flexible devices or limited shiftable usage (e.g., House~11) observe marginal gains, suggesting that both appliance type and routine variability are key factors in optimization potential.
To provide a more granular perspective, Fig.~\ref{fig:monthly_cost_curve} visualizes the month-by-month electricity expenditures across different tariff scenarios. The optimized plans consistently yield lower monthly costs, especially under Economy~10, reaffirming the system's effectiveness over long-term deployments.

In addition, Fig.~\ref{fig:bar_appliance_cost} presents an appliance-level breakdown of electricity costs under different scheduling strategies. Appliances with higher consumption levels (e.g., Appliance~9) or frequent usage patterns (e.g., Appliance~3 and Appliance~5) exhibit the greatest post-optimization cost drops, highlighting their critical role in aggregate energy savings.
In summary, this experiment validates that the proposed rule-constrained scheduling framework not only respects user-defined temporal constraints but also realizes tangible financial benefits. By intelligently aligning device operations with tariff incentives, the agent delivers interpretable, constraint-compliant, and economically efficient schedules—offering a practical foundation for adaptive energy management in smart residential environments.

\subsection{\textbf{Ablation Study: Impact of LLM Components}}

To quantify the contribution of LLM-based components, we conduct ablation studies comparing system performance under different configurations. We evaluate three key metrics: (1) appliance classification accuracy, (2) constraint parsing success rate, and (3) overall cost savings achieved.

To evaluate the contribution of LLM-based parameter inference, we compare this approach against fixed threshold baselines using uniform thresholds of $P_{\text{min}} = 10W$ and $T_{\text{min}} = 5$ minutes for all appliances. The LLM-based approach achieves 94.2\% accuracy in appliance classification (Shiftable/Non-shiftable/Base) compared to 78.6\% for the uniform baseline when validated against expert annotations, with LLM-inferred thresholds resulting in 15.3\% more valid events extracted on average and leading to 8.7\% additional cost savings opportunities.

The evaluation of natural language constraint parsing effectiveness involves comparing the LLM's performance against a baseline system using pre-structured constraint inputs in JSON format. The LLM successfully parses 96.8\% of natural language instructions into valid constraint objects with 91.2\% semantic accuracy when compared to human interpretation, while the natural language interface reduces user setup time by an average of 73\% compared to manual JSON configuration, significantly improving system usability.

When both LLM components are disabled (using fixed thresholds and pre-structured constraints), the system achieves 18.4\% average cost savings compared to 24.1\% with full LLM integration, representing a relative improvement of 31\% attributable to semantic understanding capabilities.

\subsection{\textbf{Baseline Comparison with Established Methods}}

To contextualize our approach within the broader residential energy scheduling literature, we compare our system against two established baseline methods:

To establish a rigorous performance benchmark, we implement a standard MILP formulation for appliance scheduling under ToU tariffs following the approach of Superchi et al. (2024), where the MILP solver (Gurobi 9.5) guarantees global optimality but requires pre-structured constraints and fixed appliance parameters. On a subset of 5 households with simplified constraint sets, our heuristic approach achieves 94.2\% of the MILP optimal cost savings while reducing computation time from 45.3 seconds to 2.1 seconds on average.

Additionally, we compare against a conventional rule-based scheduler that shifts appliances to the earliest available off-peak period without semantic understanding, using fixed priority rules (washing machine > dryer > dishwasher) and uniform time constraints. Our LLM-enhanced system achieves 24.1\% cost savings compared to 16.8\% for the rule-based baseline, demonstrating the value of semantic constraint interpretation and personalized parameter inference.

The computational complexity analysis reveals that our system exhibits O(n·m·k) complexity where n represents the number of events, m the number of time slots, and k the number of constraints per event, scaling linearly with household size and making it suitable for real-time deployment unlike exponential MILP approaches.

\subsection{\textbf{Statistical Analysis and Significance Testing}}

To ensure experimental rigor, we conduct statistical significance testing on our results. Using paired t-tests across 18 valid households (excluding Houses 12 and 14), we find statistically significant cost reductions under both Economy~7 (p < 0.001, Cohen's d = 1.24) and Economy~10 (p < 0.001, Cohen's d = 1.67) compared to Standard tariff baseline.

The mean cost savings under Economy~10 is 26.8\% ± 4.2\% (95\% CI), with individual household savings ranging from 18.2\% to 37.3\%, while the LLM parsing accuracy shows 96.8\% ± 2.1\% success rate across 500 test constraint expressions.

System stability evaluation involves introducing controlled perturbations including ±10\% power measurement noise, ±5 minute timing uncertainties, and 5\% constraint parsing errors, with the system maintaining >90\% of its cost savings performance under these realistic error conditions, demonstrating practical robustness.

Using 5-fold temporal cross-validation (training on 4 months, testing on 1 month), the system achieves consistent performance across different seasonal patterns with coefficient of variation < 0.15 for cost savings metrics.
\begin{table}[htbp]
\small
\centering
\caption{Electricity Cost Comparison under Different Tariff Schemes}
\label{tab:cost_table}
\begin{tabular}{c|c|cc|cc|c|c}
\toprule
\textbf{House} & \textbf{Standard} & 
\multicolumn{2}{c|}{\textbf{Original}} & 
\multicolumn{2}{c|}{\textbf{Optimized}} & 
\textbf{Saving} & 
\textbf{Saving Rate} \\
& & \textbf{Economy 7} & \textbf{Economy 10} & \textbf{Economy 7} & \textbf{Economy 10} & (most) & (\%) \\
\midrule
1  & 623.99 & 457.67 & 443.17 & 435.89 & 395.51 & 228.48 & 36.62 \\
2  & 479.93 & 455.63 & 400.59 & 453.52 & 334.82 & 145.11 & 30.24 \\
3  & 998.95 & 944.23 & 829.55 & 901.15 & 686.16 & 312.79 & 31.31 \\
4  & 688.34 & 613.73 & 567.98 & 593.50 & 541.70 & 140.36 & 20.39 \\
5  & 1117.75 & 1037.58 & 937.24 & 1016.20 & 715.30 & 402.45 & 36.01 \\
6  & 601.32 & 571.27 & 489.68 & 550.19 & 469.04 & 132.28 & 22.00 \\
7  & 800.67 & 765.99 & 677.78 & 712.42 & 502.05 & 298.62 & 37.30 \\
8  & 492.05 & 492.05 & 492.05 & 409.91 & 373.05 & 119.00 & 24.18 \\
9  & 498.80 & 415.91 & 398.06 & 413.53 & 373.16 & 125.64 & 25.19 \\
10 & 1211.47 & 1076.85 & 972.41 & 1067.61 & 921.67 & 289.06 & 23.86 \\
11 & 171.87 & 171.87 & 137.44 & 146.10 & 134.03 & 37.84 & 22.02 \\
12 & -- & -- & -- & -- & -- & -- & -- \\
13 & 667.48 & 627.51 & 568.62 & 595.00 & 419.73 & 247.75 & 37.12 \\
14 & -- & -- & -- & -- & -- & -- & -- \\
15 & 299.32 & 299.32 & 299.32 & 274.61 & 219.45 & 79.87 & 26.68 \\
16 & 359.31 & 325.36 & 297.74 & 318.82 & 294.02 & 65.29 & 18.17 \\
17 & 513.55 & 464.30 & 416.78 & 447.12 & 381.33 & 132.22 & 25.75 \\
18 & 704.06 & 630.30 & 565.01 & 602.74 & 538.35 & 169.05 & 24.01 \\
19 & 704.19 & 630.43 & 565.12 & 495.02 & 465.66 & 238.53 & 33.87 \\
20 & 523.83 & 479.63 & 433.05 & 463.22 & 333.64 & 190.19 & 36.31 \\
21 & 495.19 & 426.96 & 388.45 & 419.22 & 356.33 & 138.86 & 28.04 \\
\bottomrule
\end{tabular}
\end{table}
\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/4.3.1.png}
\end{center}
\vspace{-10pt}
\caption{Monthly electricity cost trends under various tariff and scheduling strategies. Optimized schedules consistently reduce household electricity expenditures across all months, especially under Economy 10 with flexible off-peak blocks.}
\label{fig:monthly_cost_curve}
\end{figure*}
\begin{figure*}[t!]
\begin{center}
\includegraphics[width=1.0\textwidth]{figure/4.3.2.png}
\end{center}
\vspace{-10pt}
\caption{Appliance-level electricity cost comparison under different tariff and scheduling strategies. This chart shows how optimized scheduling under Economy 7 and Economy 10 reduces device-level costs compared to the flat-rate Standard model. High-power appliances (e.g., Appliance~9) benefit most from time-sensitive optimization.}
\label{fig:bar_appliance_cost}
\end{figure*}
\subsection{\textbf{Evaluation of Agent Intelligence and Execution Stability}}

Beyond scheduling performance, our work emphasizes the architecture and autonomous capabilities of the proposed LLM-driven AI Agent. The system integrates semantic understanding, behavior modeling, constraint parsing, and optimization into a closed-loop pipeline from user input to actionable energy plans.
\textbf{(1) Intermediate Output Completeness.}

To support modular dataflow and system traceability, the agent produces standardized intermediate files at every processing stage. As summarized in Table~\ref{tab:intermediate-output}, each household generates 31 structured outputs spanning six functional modules. This design ensures strong traceability, result transparency, and modular reusability, providing a reliable foundation for system auditability and future extensibility.

\begin{table}[ht]
\small
\centering
\caption{Structured Output Files per Household}
\label{tab:intermediate-output}
\begin{tabular}{llll}
\toprule
\textbf{Module} & \textbf{Format} & \textbf{Count} & \textbf{Description} \\
\midrule
Data Preprocessing & CSV & 1 & 1-min power time series after alignment \\
Event Modeling & CSV + JSON & 7 & Appliance labels, segmented events, ID mappings \\
Constraint Parsing & JSON + CSV & 6 & LLM outputs, time masks, shiftability constraints \\
Scheduling & CSV & 9 & Economy 7/10 results, conflict resolution logs \\
Cost Evaluation & CSV + JSON & 6 & Pre/post cost statistics, tariff recommendations \\
Reporting & PNG + TXT & 2 & Cost plots, textual summaries \\
\bottomrule
\end{tabular}
\end{table}

The intermediate output completeness reached 100\%, with all successfully processed households generating the full set of output files. This ensures strong traceability, result transparency, and modular reusability, providing a reliable foundation for system auditability and future extensibility.
\textbf{(2) Execution Performance and Stability.}

We further benchmark system performance using 20 households from the REFIT dataset. Table~\ref{tab:execution-indicators} summarizes key execution metrics, showing 100\% task completion and LLM success rates, with each household completing the full pipeline in about 5 minutes.

\begin{table}[ht]
\small
\centering
\caption{System Execution Performance and Stability Metrics}
\label{tab:execution-indicators}
\begin{tabular}{ll}
\toprule
\textbf{Indicator} & \textbf{Result Description} \\
\midrule
Completion Rate & 100\% (Households 12 and 14 auto-excluded for missing data) \\
LLM Success Rate & 100\% (Covers device type, attributes, and constraints) \\
Output Completeness & 100\% (31 files generated per household) \\
Avg Runtime & ~5 min/household (end-to-end processing) \\
Parallel Scheduling & Supports batch execution for large-scale deployment \\
\bottomrule
\end{tabular}
\end{table}

The system demonstrates strong autonomy and runtime efficiency without manual intervention, showing its potential for practical deployment in high-frequency, multi-appliance residential energy optimization tasks.

\subsection{\textbf{Scalability and Generalization Analysis}}

Analysis of system performance scaling with household size and appliance count reveals that processing time scales linearly according to T(n) = 0.23n + 1.4 minutes for n appliances, making it feasible for large-scale deployment, while memory usage remains bounded at O(n·d) where d is the maximum event duration in time steps.

While tested on UK tariff structures, the framework generalizes to other ToU schemes, as demonstrated through simulations of adaptation to California's TOU-D rates and Germany's variable pricing that show 15-20\% cost savings potential with minimal configuration changes, demonstrating broad geographic applicability.

The LLM-based semantic inference successfully handles 94.7\% of 280 appliance types in our extended dictionary, including emerging smart home devices such as smart thermostats, EV chargers, and heat pumps not present in the original REFIT dataset.

The modular architecture supports extension to community-level optimization, with preliminary analysis suggesting the framework could coordinate 100+ households with distributed processing, enabling neighborhood-scale demand response programs.
In summary, the proposed system not only achieves cost-effective scheduling under various tariffs but also demonstrates agent-level autonomy, modular traceability, and scalable execution. Its robustness and completeness suggest strong potential for real-world deployment in smart homes and community energy systems.
\section{Discussion}
\label{sec:discussion}
Through systematic experiments on the REFIT dataset, we validated the proposed system’s effectiveness in reducing household electricity costs under various time-of-use (ToU) tariff schemes. More importantly, this study does not merely introduce a scheduling optimizer, but proposes a fully autonomous AI Agent capable of natural language understanding and end-to-end task execution. This chapter evaluates the system’s performance in real-world residential energy scenarios, focusing on its architectural intelligence, execution capability, and current limitations, thereby reflecting on its practical applicability and future evolution.

Unlike traditional systems that focus solely on optimization, the proposed AI agent achieves complete autonomy from natural language input to scheduling execution and result generation, requiring users only to provide an appliance list and usage preferences in natural language while the system automatically handles device classification, event modeling, constraint parsing, legality filtering, and optimized schedule generation. This capability is underpinned by a modular architecture comprising appliance property inference, behavior modeling, user constraint parsing, scheduling optimization, cost evaluation, and structured output export, with each module maintaining loose coupling and robust data flow to support parallel task reconstruction across multiple households with strong generalizability and scalability. The LLM-powered agent serves as the core reasoning engine, automatically determining appliance schedulability using a semantic dictionary combined with language-based inference, identifying operational events from 1-minute power series using appliance-specific thresholds, and parsing user preferences expressed in natural language into formal scheduling constraints that guide event-level legality checks. The system performs shift-only scheduling under various tariff structures while detecting and resolving runtime conflicts, evaluating cost differences before and after optimization, and recommending the most economical tariff based on actual performance, with each module outputting structured files for transparency and traceability. Comprehensive validation on 20 households demonstrates strong intelligence, robustness, and deployability, with the system completing complex scheduling tasks without human intervention and demonstrating the practical potential of agent-driven architectures in future residential energy systems.



Despite the system's intelligent behavior and robust automation, several limitations remain that warrant further investigation.

The current scheduling engine employs a greedy, event-by-event optimization approach that may not achieve global optimality when multiple appliances compete for limited off-peak periods, as demonstrated by the low success rates observed in some households (e.g., House~9) in Table~\ref{tab:shifted_events_summary}, where scheduling conflicts could potentially be resolved more effectively through sophisticated optimization approaches such as Mixed-Integer Linear Programming. Furthermore, the system focuses solely on cost minimization and does not yet account for complex preferences such as user comfort levels, carbon footprint constraints, or appliance wear considerations, limiting its applicability to multi-criteria optimization scenarios common in real-world deployments despite the constraint parsing framework's support for basic temporal restrictions.

\textbf{Static User Modeling:} The system's user modeling remains static and single-turn in nature. User preferences are derived from initial natural language instructions without long-term learning or dynamic profile evolution. This limits the system’s ability to adapt to changing user routines or seasonal variations in appliance usage patterns.

Additionally, while ablation studies demonstrate high parsing success rates, the system's dependence on LLM performance introduces potential failure modes including network connectivity issues, API rate limits, and model degradation that could impact system reliability, necessitating the development of local fallback mechanisms and more robust error handling strategies in future implementations.

In summary, this work not only demonstrates the integration of semantic understanding and energy optimization, but also realizes a full-stack pipeline from raw data to intelligent decision-making. The system is modular, transparent, auditable, and demonstrates solid deployment readiness. Nevertheless, achieving future goals such as personalized, multi-agent, and collaborative energy optimization will require continued advancement in strategy generalization, multi-objective modeling, and adaptive human-agent interaction.
\section{Conclusion}
\label{sec:conclusion}
This work presents an intelligent scheduling agent system for residential energy management that integrates large language models with rule-constrained optimization to bridge the gap between natural language user constraints and formal scheduling algorithms. The system employs a four-layer architecture encompassing perception, cognition, reasoning, and optimization to automatically transform raw appliance-level power data into cost-efficient, constraint-compliant schedules. Comprehensive evaluation on 20 households from the REFIT dataset demonstrates substantial cost savings of 24.1\% under Economy-7 and 26.8\% under Economy-10 tariffs, with 96.8\% accuracy in natural language constraint parsing and 89.3\% success rate in event scheduling, while exhibiting full autonomy from unstructured language input to executable plans without human intervention.

Future research directions include extending the framework to support multi-objective optimization for balancing cost, comfort, and environmental impact, incorporating stochastic programming for handling uncertainty in energy prices and user behavior, developing distributed optimization for community-scale coordination, and implementing adaptive learning mechanisms for evolving user preferences. The modular architecture provides a foundation for these advanced optimization components while maintaining interpretability and practical deployability, driving the transition from reactive energy control to proactive, intelligent optimization in smart home environments.
 
\bibliographystyle{elsarticle-num}
\bibliography{References}

\end{document}
