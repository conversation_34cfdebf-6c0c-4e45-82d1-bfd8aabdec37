\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\emailauthor{<EMAIL>}{<PERSON><PERSON><PERSON><PERSON>\corref {cor1}}
\Newlabel{cor1}{1}
\citation{chen2023federated,anvari2022data,sykiotis2023performance}
\citation{he2023msdc,ouzine2022overview}
\citation{rafiq2024review,kaselimi2022towards}
\citation{ye2023review,muttaqee2024time}
\citation{anvari2022data}
\citation{russell2016artificial}
\citation{murray2016refit}
\citation{brown2020language}
\providecommand \oddpage@label [2]{}
\Newlabel{EIT}{a}
\Newlabel{dtu}{b}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1}{section.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces System architecture of the Residential Energy Cognition Agent}}{3}{figure.1}\protected@file@percent }
\newlabel{fig:00overview}{{1}{3}{System architecture of the Residential Energy Cognition Agent}{figure.1}{}}
\citation{lin2024multiscale,chen2021smart,10121340}
\citation{wu2021non,kumar2023time}
\citation{jia2021sequence}
\citation{schirmer2022non,fabri2025fostering}
\citation{yuan2022multi,mirzaei2025flexibility}
\citation{albogamy2022optimal}
\citation{tostado2021milp}
\citation{6162795}
\citation{wu2022impact,fiorotti2024day}
\citation{kumar2023multi,zhang2022review}
\@writefile{toc}{\contentsline {section}{\numberline {2}Related Work}{4}{section.2}\protected@file@percent }
\newlabel{sec:related}{{2}{4}{Related Work}{section.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}\textbf  {Non-Intrusive Load Monitoring and Household Energy Modeling}}{4}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}\textbf  {ToU-Aware Residential Load Scheduling}}{4}{subsection.2.2}\protected@file@percent }
\citation{superchi2024optimization,masaud2024optimal}
\citation{shui2024optimal,srivastava2024profit}
\citation{rambabu2024internet,jamal2024rule}
\citation{wang2025analysing,wang2024scalable}
\citation{nguyen2024modelling,liu2024enhancing}
\citation{ajagekar2024energy,khodadadi2024data}
\citation{dasgupta2025smart,rivkin2024aiot}
\citation{giudici2024designing,michelon2025large,yonekura2024generating}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}\textbf  {Constraint-Driven Appliance Scheduling}}{5}{subsection.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}\textbf  {LLMs for Semantic User Behavior Modeling}}{5}{subsection.2.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}Methodology}{6}{section.3}\protected@file@percent }
\newlabel{sec:methodology}{{3}{6}{Methodology}{section.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}\textbf  {Agent Architecture and Functional Modules}}{6}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}\textbf  {Load Sensing and Alignment Module}}{7}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces A unified temporal processing pipeline that transforms multi-resolution appliance-level data (e.g., REFIT: 8s, Smart Meters: 5min, CER: 15min) into behavior-preserving, aligned time series sequences for downstream modeling.}}{8}{figure.2}\protected@file@percent }
\newlabel{fig:3.2}{{2}{8}{A unified temporal processing pipeline that transforms multi-resolution appliance-level data (e.g., REFIT: 8s, Smart Meters: 5min, CER: 15min) into behavior-preserving, aligned time series sequences for downstream modeling}{figure.2}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {1) Temporal Detection and Consistency Verification}}{8}{section*.1}\protected@file@percent }
\newlabel{eq:consistency}{{1}{8}{\textbf {1) Temporal Detection and Consistency Verification}}{equation.3.1}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {2) Temporal Alignment and Adaptive Aggregation}}{8}{section*.2}\protected@file@percent }
\newlabel{eq:aggregation}{{2}{8}{\textbf {2) Temporal Alignment and Adaptive Aggregation}}{equation.3.2}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {3) Structured Sequence Construction and Output Buffering}}{9}{section*.3}\protected@file@percent }
\newlabel{eq:sequence}{{3}{9}{\textbf {3) Structured Sequence Construction and Output Buffering}}{equation.3.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}\textbf  {Appliance Behavior Modeling and Shiftability Identification}}{9}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Semantic behavior modeling pipeline: LLM-based appliance inference (left), event segmentation based on power signals (center), and generation of shiftability-aware structured logs (right).}}{10}{figure.3}\protected@file@percent }
\newlabel{fig:3.3}{{3}{10}{Semantic behavior modeling pipeline: LLM-based appliance inference (left), event segmentation based on power signals (center), and generation of shiftability-aware structured logs (right)}{figure.3}{}}
\newlabel{eq:llm_params}{{4}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.4}{}}
\newlabel{eq:segmentation}{{5}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.5}{}}
\newlabel{eq:event_energy}{{6}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces Structured fields of an appliance operation event}}{11}{table.1}\protected@file@percent }
\newlabel{tab:event_fields}{{1}{11}{Structured fields of an appliance operation event}{table.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Left: Detected appliance working events and associated Time-of-Use (ToU) tariffs. Right: Cost comparison under different tariff policies and AI agent's recommended strategy.}}{12}{figure.4}\protected@file@percent }
\newlabel{fig:3.4.1}{{4}{12}{Left: Detected appliance working events and associated Time-of-Use (ToU) tariffs. Right: Cost comparison under different tariff policies and AI agent's recommended strategy}{figure.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.4}\textbf  {Tariff Modeling and Constraint Parsing}}{12}{subsection.3.4}\protected@file@percent }
\newlabel{eq:tariff_cost}{{7}{12}{\textbf {Tariff Modeling and Constraint Parsing}}{equation.3.7}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Left: LLMs interpret user instructions into structured constraints. Right: appliance-level constraint segmentation and output for downstream scheduling.}}{13}{figure.5}\protected@file@percent }
\newlabel{fig:3.4.2}{{5}{13}{Left: LLMs interpret user instructions into structured constraints. Right: appliance-level constraint segmentation and output for downstream scheduling}{figure.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces Structured representation of user-defined scheduling constraints}}{13}{table.2}\protected@file@percent }
\newlabel{tab:user_constraints_example}{{2}{13}{Structured representation of user-defined scheduling constraints}{table.2}{}}
