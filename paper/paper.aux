\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\emailauthor{<EMAIL>}{<PERSON><PERSON><PERSON><PERSON>\corref {cor1}}
\Newlabel{cor1}{1}
\citation{chen2023federated,anvari2022data,sykiotis2023performance}
\citation{he2023msdc,ouzine2022overview}
\citation{rafiq2024review,kaselimi2022towards}
\citation{ye2023review,muttaqee2024time}
\citation{anvari2022data}
\citation{russell2016artificial}
\citation{murray2016refit}
\citation{brown2020language}
\providecommand \oddpage@label [2]{}
\Newlabel{EIT}{a}
\Newlabel{dtu}{b}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{2}{section.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces System architecture of the Residential Energy Cognition Agent}}{3}{figure.1}\protected@file@percent }
\newlabel{fig:00overview}{{1}{3}{System architecture of the Residential Energy Cognition Agent}{figure.1}{}}
\citation{lin2024multiscale,chen2021smart,10121340}
\citation{wu2021non,kumar2023time}
\citation{jia2021sequence}
\citation{schirmer2022non,fabri2025fostering}
\citation{yuan2022multi,mirzaei2025flexibility}
\citation{albogamy2022optimal}
\citation{tostado2021milp}
\@writefile{toc}{\contentsline {section}{\numberline {2}Related Work}{4}{section.2}\protected@file@percent }
\newlabel{sec:related}{{2}{4}{Related Work}{section.2}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}\textbf  {Non-Intrusive Load Monitoring and Household Energy Modeling}}{4}{subsection.2.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}\textbf  {ToU-Aware Residential Load Scheduling}}{4}{subsection.2.2}\protected@file@percent }
\citation{6162795}
\citation{wu2022impact,fiorotti2024day}
\citation{kumar2023multi,zhang2022review}
\citation{superchi2024optimization,masaud2024optimal}
\citation{shui2024optimal,srivastava2024profit}
\citation{rambabu2024internet,jamal2024rule}
\citation{wang2025analysing,wang2024scalable}
\citation{nguyen2024modelling,liu2024enhancing}
\citation{ajagekar2024energy,khodadadi2024data}
\citation{dasgupta2025smart,rivkin2024aiot}
\citation{giudici2024designing,michelon2025large,yonekura2024generating}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}\textbf  {Constraint-Driven Appliance Scheduling}}{5}{subsection.2.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}\textbf  {LLMs for Semantic User Behavior Modeling}}{6}{subsection.2.4}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {3}Methodology}{6}{section.3}\protected@file@percent }
\newlabel{sec:methodology}{{3}{6}{Methodology}{section.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}\textbf  {Agent Architecture and Functional Modules}}{6}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}\textbf  {Load Sensing and Alignment Module}}{7}{subsection.3.2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces A unified temporal processing pipeline that transforms multi-resolution appliance-level data (e.g., REFIT: 8s, Smart Meters: 5min, CER: 15min) into behavior-preserving, aligned time series sequences for downstream modeling.}}{8}{figure.2}\protected@file@percent }
\newlabel{fig:3.2}{{2}{8}{A unified temporal processing pipeline that transforms multi-resolution appliance-level data (e.g., REFIT: 8s, Smart Meters: 5min, CER: 15min) into behavior-preserving, aligned time series sequences for downstream modeling}{figure.2}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {1) Temporal Detection and Consistency Verification}}{8}{section*.1}\protected@file@percent }
\newlabel{eq:consistency}{{1}{8}{\textbf {1) Temporal Detection and Consistency Verification}}{equation.3.1}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {2) Temporal Alignment and Adaptive Aggregation}}{8}{section*.2}\protected@file@percent }
\newlabel{eq:aggregation}{{2}{8}{\textbf {2) Temporal Alignment and Adaptive Aggregation}}{equation.3.2}{}}
\@writefile{toc}{\contentsline {paragraph}{\textbf  {3) Structured Sequence Construction and Output Buffering}}{9}{section*.3}\protected@file@percent }
\newlabel{eq:sequence}{{3}{9}{\textbf {3) Structured Sequence Construction and Output Buffering}}{equation.3.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}\textbf  {Appliance Behavior Modeling and Shiftability Identification}}{9}{subsection.3.3}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Semantic behavior modeling pipeline: LLM-based appliance inference (left), event segmentation based on power signals (center), and generation of shiftability-aware structured logs (right).}}{10}{figure.3}\protected@file@percent }
\newlabel{fig:3.3}{{3}{10}{Semantic behavior modeling pipeline: LLM-based appliance inference (left), event segmentation based on power signals (center), and generation of shiftability-aware structured logs (right)}{figure.3}{}}
\newlabel{eq:llm_params}{{4}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.4}{}}
\newlabel{eq:segmentation}{{5}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.5}{}}
\newlabel{eq:event_energy}{{6}{10}{\textbf {Appliance Behavior Modeling and Shiftability Identification}}{equation.3.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {1}{\ignorespaces Structured fields of an appliance operation event}}{11}{table.1}\protected@file@percent }
\newlabel{tab:event_fields}{{1}{11}{Structured fields of an appliance operation event}{table.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.4}\textbf  {Tariff Modeling and Constraint Parsing}}{11}{subsection.3.4}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Left: Detected appliance working events and associated Time-of-Use (ToU) tariffs. Right: Cost comparison under different tariff policies and AI agent's recommended strategy.}}{12}{figure.4}\protected@file@percent }
\newlabel{fig:3.4.1}{{4}{12}{Left: Detected appliance working events and associated Time-of-Use (ToU) tariffs. Right: Cost comparison under different tariff policies and AI agent's recommended strategy}{figure.4}{}}
\newlabel{eq:tariff_cost}{{7}{12}{\textbf {Tariff Modeling and Constraint Parsing}}{equation.3.7}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Left: LLMs interpret user instructions into structured constraints. Right: appliance-level constraint segmentation and output for downstream scheduling.}}{13}{figure.5}\protected@file@percent }
\newlabel{fig:3.4.2}{{5}{13}{Left: LLMs interpret user instructions into structured constraints. Right: appliance-level constraint segmentation and output for downstream scheduling}{figure.5}{}}
\@writefile{lot}{\contentsline {table}{\numberline {2}{\ignorespaces Structured representation of user-defined scheduling constraints}}{13}{table.2}\protected@file@percent }
\newlabel{tab:user_constraints_example}{{2}{13}{Structured representation of user-defined scheduling constraints}{table.2}{}}
\newlabel{eq:llm_mapping}{{8}{14}{\textbf {Tariff Modeling and Constraint Parsing}}{equation.3.8}{}}
\newlabel{eq:conflict_resolution}{{9}{14}{\textbf {Tariff Modeling and Constraint Parsing}}{equation.3.9}{}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces LLM-Based Constraint Parsing Algorithm}}{15}{algocf.1}\protected@file@percent }
\newlabel{alg:constraint_parsing}{{1}{15}{\textbf {Tariff Modeling and Constraint Parsing}}{algocf.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.5}\textbf  {Scheduling Optimization and Adaptive Recommendation}}{15}{subsection.3.5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Top: Agent receives time-of-use tariffs and constraint inputs. Middle: Optimization flow based on rule-constrained heuristic scheduling strategy; results are evaluated across candidate time windows under ToU tariffs. Bottom: Tabular output of recommended schedules and tariff evaluation.}}{16}{figure.6}\protected@file@percent }
\newlabel{fig:3.5}{{6}{16}{Top: Agent receives time-of-use tariffs and constraint inputs. Middle: Optimization flow based on rule-constrained heuristic scheduling strategy; results are evaluated across candidate time windows under ToU tariffs. Bottom: Tabular output of recommended schedules and tariff evaluation}{figure.6}{}}
\newlabel{eq:optimization_objective}{{10}{16}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.10}{}}
\newlabel{eq:deadline_constraint}{{11}{16}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.11}{}}
\newlabel{eq:forbidden_constraint}{{12}{16}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.12}{}}
\newlabel{eq:conflict_constraint}{{13}{16}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.13}{}}
\newlabel{eq:tariff_function}{{14}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.14}{}}
\newlabel{eq:forbidden_set}{{15}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.15}{}}
\newlabel{eq:deadline_set}{{16}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.16}{}}
\newlabel{eq:feasible_set}{{17}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.17}{}}
\newlabel{eq:cost_estimation}{{18}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.18}{}}
\newlabel{eq:schedule_selection}{{19}{17}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.19}{}}
\@writefile{loa}{\contentsline {algocf}{\numberline {2}{\ignorespaces Rule-Constrained Cost-Aware Scheduling Algorithm}}{18}{algocf.2}\protected@file@percent }
\newlabel{alg:scheduling}{{2}{18}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{algocf.2}{}}
\newlabel{eq:priority_function}{{20}{18}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.20}{}}
\newlabel{eq:cost_savings}{{21}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.21}{}}
\newlabel{eq:max_savings}{{22}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.22}{}}
\newlabel{eq:penalty_function}{{23}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.23}{}}
\newlabel{eq:sort_complexity}{{24}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.24}{}}
\newlabel{eq:schedule_complexity}{{25}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.25}{}}
\newlabel{eq:total_complexity}{{26}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.26}{}}
\newlabel{eq:approximation_ratio}{{27}{19}{\textbf {Scheduling Optimization and Adaptive Recommendation}}{equation.3.27}{}}
\@writefile{toc}{\contentsline {section}{\numberline {4}Experiments}{20}{section.4}\protected@file@percent }
\newlabel{sec:experiments}{{4}{20}{Experiments}{section.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}\textbf  {Experimental Setup}}{20}{subsection.4.1}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {3}{\ignorespaces ToU Tariff Configurations}}{21}{table.3}\protected@file@percent }
\newlabel{tab:tariff-config}{{3}{21}{ToU Tariff Configurations}{table.3}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}\textbf  {Shiftable Event Identification and Filtering}}{21}{subsection.4.2}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {4}{\ignorespaces Shifted Events Summary under Tariff Schemes}}{22}{table.4}\protected@file@percent }
\newlabel{tab:shifted_events_summary}{{4}{22}{Shifted Events Summary under Tariff Schemes}{table.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.3}\textbf  {Cost Evaluation under Original and Optimized Scheduling}}{24}{subsection.4.3}\protected@file@percent }
\newlabel{eq:abs_savings}{{29}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.29}{}}
\newlabel{eq:savings_rate}{{30}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.30}{}}
\newlabel{eq:cost_per_kwh}{{31}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.31}{}}
\newlabel{eq:success_rate}{{32}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.32}{}}
\newlabel{eq:compliance_rate}{{33}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.33}{}}
\newlabel{eq:peak_reduction}{{34}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.34}{}}
\newlabel{eq:load_factor}{{35}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.35}{}}
\newlabel{eq:dr_potential}{{36}{24}{\textbf {Cost Evaluation under Original and Optimized Scheduling}}{equation.4.36}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.4}\textbf  {Ablation Study: Impact of LLM Components}}{25}{subsection.4.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.5}\textbf  {Baseline Comparison with Established Methods}}{25}{subsection.4.5}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.6}\textbf  {Statistical Analysis and Significance Testing}}{26}{subsection.4.6}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.7}\textbf  {Evaluation of Agent Intelligence and Execution Stability}}{26}{subsection.4.7}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {5}{\ignorespaces Electricity Cost Comparison under Different Tariff Schemes}}{27}{table.5}\protected@file@percent }
\newlabel{tab:cost_table}{{5}{27}{Electricity Cost Comparison under Different Tariff Schemes}{table.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Monthly electricity cost trends under various tariff and scheduling strategies. Optimized schedules consistently reduce household electricity expenditures across all months, especially under Economy 10 with flexible off-peak blocks.}}{28}{figure.7}\protected@file@percent }
\newlabel{fig:monthly_cost_curve}{{7}{28}{Monthly electricity cost trends under various tariff and scheduling strategies. Optimized schedules consistently reduce household electricity expenditures across all months, especially under Economy 10 with flexible off-peak blocks}{figure.7}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces Appliance-level electricity cost comparison under different tariff and scheduling strategies. This chart shows how optimized scheduling under Economy 7 and Economy 10 reduces device-level costs compared to the flat-rate Standard model. High-power appliances (e.g., Appliance~9) benefit most from time-sensitive optimization.}}{28}{figure.8}\protected@file@percent }
\newlabel{fig:bar_appliance_cost}{{8}{28}{Appliance-level electricity cost comparison under different tariff and scheduling strategies. This chart shows how optimized scheduling under Economy 7 and Economy 10 reduces device-level costs compared to the flat-rate Standard model. High-power appliances (e.g., Appliance~9) benefit most from time-sensitive optimization}{figure.8}{}}
\@writefile{lot}{\contentsline {table}{\numberline {6}{\ignorespaces Structured Output Files per Household}}{29}{table.6}\protected@file@percent }
\newlabel{tab:intermediate-output}{{6}{29}{Structured Output Files per Household}{table.6}{}}
\@writefile{lot}{\contentsline {table}{\numberline {7}{\ignorespaces System Execution Performance and Stability Metrics}}{29}{table.7}\protected@file@percent }
\newlabel{tab:execution-indicators}{{7}{29}{System Execution Performance and Stability Metrics}{table.7}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {4.8}\textbf  {Scalability and Generalization Analysis}}{29}{subsection.4.8}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {5}Discussion}{30}{section.5}\protected@file@percent }
\newlabel{sec:discussion}{{5}{30}{Discussion}{section.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {5.1}\textbf  {System Architecture and Intelligent Capabilities}}{30}{subsection.5.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {5.2}\textbf  {Limitations}}{31}{subsection.5.2}\protected@file@percent }
\bibstyle{elsarticle-num}
\bibdata{References}
\@writefile{toc}{\contentsline {section}{\numberline {6}Conclusion}{32}{section.6}\protected@file@percent }
\newlabel{sec:conclusion}{{6}{32}{Conclusion}{section.6}{}}
\gdef \@abspage@last{32}
